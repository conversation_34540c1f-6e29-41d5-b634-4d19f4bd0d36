public class student {
    private String name;
    private int age;

    public student(String name, int age) {
        this.name = name;
        this.age = age;
    }

    // Getter method to access the name
    public String getName() {
        return name;
    }

    // Setter method to set the name
    public void setName(String name) {
        this.name = name;
    }

    // Getter method to access the age
    public int getAge() {
        return age;
    }

    // Setter method to set the age
    public void setAge(int age) {
        if (age >= 0 && age <= 120) {
            this.age = age;
        } else {
            System.out.println("Invalid age!");
        }
    }

    // Other methods and behaviors can be defined here

    // Main method for testing the student class
    public static void main(String[] args) {
        student student1 = new student("Alice", 20);

        // Accessing the object's attributes using getter methods
        System.out.println("Name: " + student1.getName());
        System.out.println("Age: " + student1.getAge());

        // Modifying the object's attributes using setter methods
        student1.setName("Bob");
        student1.setAge(25);

        System.out.println("Name: " + student1.getName());
        System.out.println("Age: " + student1.getAge());

        // Attempt to set an invalid age
        student1.setAge(150); // This will print "Invalid age!"
    }
}
