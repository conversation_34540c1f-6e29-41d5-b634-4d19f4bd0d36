import java.util.Scanner;

public class simplecalculator {
    public static void main(String[] args) {
        double result;
        char op;
        try (Scanner sc = new Scanner(System.in)) {
            System.out.println("enter the number");
            double num1 = sc.nextDouble();
            double num2 = sc.nextDouble();
            System.out.println("choosre the operator(+,-,*,/)");

            op = sc.next().charAt(0);
            switch (op) {
                case '+':
                    result = num1 + num2;                   System.out.print("The result is given as follows:");
                    System.out.println(result);
                    break;
                case '-':
                    result = num1 - num2;
                    System.out.print("The result is given as follows:");
                    System.out.println(result);
                    break;
                case '*':
                    result = num1 * num2;
                    System.out.print("The result is given as follows:");
                    System.out.println(result);
                    break;
                case '/':
                    result = num1 / num2;
                    System.out.print("The result is given as follows:");
                    System.out.println(result);
                    break;
                default:
                    System.out.printf("Error! Enter correct operator");
                    return;

            }
            // System.out.print("The result is given as follows:");
            // System.out.printf(num1 + " " + op + " " + num2 + " = " + result);
        }
    }
}
