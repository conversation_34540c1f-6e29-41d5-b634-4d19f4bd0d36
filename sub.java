import java.util.*;;

public class sub {
    public static void main(String[] args) {
        // Check if command line arguments are provided
        if (args.length >= 2) {
            // Method 1: Using command line arguments
            try {
                int num1 = Integer.parseInt(args[0]);
                int num2 = Integer.parseInt(args[1]);
                int result = num1 - num2;
                System.out.println("Subtraction of " + num1 + " - " + num2 + " = " + result);
            } catch (NumberFormatException e) {
                System.out.println("Error: Please provide valid integer arguments");
            }
        } else {
            // Method 2: Using predefined values
            int num1 = 10;
            int num2 = 5;
            int result = num1 - num2;
            System.out.println("Subtraction of " + num1 + " - " + num2 + " = " + result);

            // Method 3: Using Scanner (uncomment to use)
            /*
             * Scanner sc = new Scanner(System.in);
             * System.out.println("\nLet's try with your input:");
             * System.out.print("Enter first number: ");
             * num1 = sc.nextInt();
             * System.out.print("Enter second number: ");
             * num2 = sc.nextInt();
             * sc.close();
             * result = num1 - num2;
             * System.out.println("Result: " + result);
             */
        }
    }
}
