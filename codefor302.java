import java.util.Scanner;

public class codefor302 {

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        int n = scanner.nextInt();
        int m = scanner.nextInt();

        int[] array = new int[n];
        for (int i = 0; i < n; i++) {
            array[i] = scanner.nextInt();
        }

        int[][] queries = new int[m][2];
        for (int i = 0; i < m; i++) {
            queries[i][0] = scanner.nextInt();
            queries[i][1] = scanner.nextInt();
        }

        int[] results = solveQueries(n, m, array, queries);
        for (int result : results) {
            System.out.print(result + " ");
        }

        scanner.close(); // Close the scanner to prevent resource leak
    }

    public static int[] solveQueries(int n, int m, int[] array, int[][] queries) {

        int countMinusOne = 0;
        int countOne = 0;
        for (int value : array) {
            if (value == -1) {
                countMinusOne++;
            } else {
                countOne++;
            }
        }

        int[] results = new int[m];
        for (int i = 0; i < m; i++) {
            int li = queries[i][0];
            int ri = queries[i][1];

            int subarrayMinusOne = 0;
            int subarrayOne = 0;
            for (int j = li - 1; j < ri; j++) {
                if (array[j] == -1) {
                    subarrayMinusOne++;
                } else {
                    subarrayOne++;
                }
            }

            if ((countMinusOne - subarrayMinusOne) == (countOne - subarrayOne)) {
                results[i] = 1;
            } else {
                results[i] = 0;
            }
        }

        return results;
    }
}
