import java.util.HashMap;

public class unique {

    public static void main(String[] args) {

        int[] arr = { 5, 5, 5, 2, 2, 2, 1 };

        HashMap<Integer, Integer> mp = new HashMap<>();

        for (int a : arr) {
            mp.put(a, mp.getOrDefault(a, 0) + 1);
        }

        for (int a : mp.keySet()) {
            if (mp.get(a) == 1) {
                System.out.println(a);
            }
        }
    }
}
