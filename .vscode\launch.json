{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "gap",
            "request": "launch",
            "mainClass": "gap",
            "projectName": "JAVA"
        },
        {
            "type": "java",
            "name": "simplecalculator",
            "request": "launch",
            "mainClass": "simplecalculator",
            "projectName": "JAVA"
        },
        {
            "type": "java",
            "name": "unique",
            "request": "launch",
            "mainClass": "unique",
            "projectName": "JAVA"
        },
        {
            "type": "java",
            "name": "div",
            "request": "launch",
            "mainClass": "div",
            "projectName": "JAVA"
        },
        {
            "type": "java",
            "name": "pattern2",
            "request": "launch",
            "mainClass": "pattern2",
            "projectName": "JAV<PERSON>"
        },
        {
            "type": "java",
            "name": "mul",
            "request": "launch",
            "mainClass": "mul",
            "projectName": "JAVA"
        },
        {
            "type": "java",
            "name": "soroban",
            "request": "launch",
            "mainClass": "registration.soroban",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "Main",
            "request": "launch",
            "mainClass": "Main",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "Main1",
            "request": "launch",
            "mainClass": "Main1",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "HelloWorld",
            "request": "launch",
            "mainClass": "HelloWorld",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "first",
            "request": "launch",
            "mainClass": "first",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "mul",
            "request": "launch",
            "mainClass": "mul",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "div",
            "request": "launch",
            "mainClass": "div",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "sub",
            "request": "launch",
            "mainClass": "sub",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "checkint",
            "request": "launch",
            "mainClass": "checkint",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "kmTomiles",
            "request": "launch",
            "mainClass": "kmTomiles",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "per",
            "request": "launch",
            "mainClass": "per",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "SpanningTree",
            "request": "launch",
            "mainClass": "SpanningTree",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "unique",
            "request": "launch",
            "mainClass": "unique",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "generic",
            "request": "launch",
            "mainClass": "output.generic",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "pyr",
            "request": "launch",
            "mainClass": "pyr",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "pattern2",
            "request": "launch",
            "mainClass": "pattern2",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "pattern4",
            "request": "launch",
            "mainClass": "pattern4",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "pattern5",
            "request": "launch",
            "mainClass": "pattern5",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "pattren8",
            "request": "launch",
            "mainClass": "pattren8",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "cod_411",
            "request": "launch",
            "mainClass": "cod_411",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "PasswordChecker",
            "request": "launch",
            "mainClass": "PasswordChecker",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "Magnets",
            "request": "launch",
            "mainClass": "Magnets",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "codefor302",
            "request": "launch",
            "mainClass": "codefor302",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "main",
            "request": "launch",
            "mainClass": "main",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "vehicle",
            "request": "launch",
            "mainClass": "vehicle",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "removewhitespace",
            "request": "launch",
            "mainClass": "removewhitespace",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "reverse",
            "request": "launch",
            "mainClass": "reverse",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "calculator",
            "request": "launch",
            "mainClass": "calculator",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "name",
            "request": "launch",
            "mainClass": "name",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "cf_363",
            "request": "launch",
            "mainClass": "registration.cf_363",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "patt10",
            "request": "launch",
            "mainClass": "registration.patt10",
            "projectName": "JAVA_64301a60"
        },
        {
            "type": "java",
            "name": "c",
            "request": "launch",
            "mainClass": "registration.c",
            "projectName": "JAVA_64301a60"
        }
    ]
}