import java.util.Scanner;
import java.util.Stack;

public class Main {
    public static int minLengthAfterOperation(int n, String s) {
        Stack<Character> stack = new Stack<>();

        for (char digit : s.toCharArray()) {
            if (!stack.isEmpty() && stack.peek() != digit) {
                stack.pop();
            } else {
                stack.push(digit);
            }
        }

        return stack.size();
    }

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

       
        int n = scanner.nextInt();
        String s = scanner.next();

        
        System.out.println(minLengthAfterOperation(n, s));

        scanner.close();
    }
}
