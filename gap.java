import java.util.*;;

public class gap {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        int n = sc.nextInt();
        int m = sc.nextInt();
        int count = 0;
        for (int i = 1; i <= n; i++) {
            for (int j = 1; j <= m; j++) {
                if ((i + j) / 5 == 0) {
                    count++;
                }

            }
        }
        System.out.println(count);
    }
}
