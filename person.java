public class person {
    private String name;
    private int age;

    public person(String name, int age) {
        this.name = name;
        this.age = age;
    }

    public String getname() {
        return name;
    }

    public int getage() {
        return age;
    }

    // Main method for testing the person class
    public static void main(String args[]) {
        person person1 = new person("ayush", 20);
        person person2 = new person("aditya", 24);
        System.out.println("Person 1 - Name: " + person1.getname() + ", Age: " + person1.getage());
        System.out.println("Person 2 - Name: " + person2.getname() + ", Age: " + person2.getage());
    }
}
