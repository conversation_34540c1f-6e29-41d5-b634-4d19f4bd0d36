import java.util.Scanner;

public class cod_411 {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        String password = scanner.nextLine();

        if (isComplexPassword(password)) {
            System.out.println("correct");
        } else {
            System.out.println("too weak");
        }
    }

    public static boolean isComplexPassword(String password) {
       
        if (password.length() < 5) {
            return false;
        }

        
        boolean containsUppercase = false;
        boolean containsLowercase = false;
        boolean containsDigit = false;

        for (char ch : password.toCharArray()) {
            if (Character.isUpperCase(ch)) {
                containsUppercase = true;
            } else if (Character.isLowerCase(ch)) {
                containsLowercase = true;
            } else if (Character.isDigit(ch)) {
                containsDigit = true;
            }
        }

        return containsUppercase && containsLowercase && containsDigit;
    }
}
