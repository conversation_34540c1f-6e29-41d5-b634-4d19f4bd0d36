import java.util.Scanner;

public class pattern6 {
    public static void main(String args[]) {
        Scanner Sc = new Scanner(System.in);
        int n = Sc.nextInt();
        int space = n - 1;
        int star = 1;
        for (int i = 1; i <= n; i++) {
            for (int j = 1; j <= space; j++) {
                System.out.print(" ");
            }
            for (int k = 1; k <= star; k++) {
                System.out.print("*");
            }
            System.out.println();
            space--;
            star++;

        }

    }
}
