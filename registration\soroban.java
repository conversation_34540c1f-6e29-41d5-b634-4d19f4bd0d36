import java.util.Scanner;

public class soroban {
    public static void main(String[] args) {
        String[] arr = new String[10];
        arr[0] = "O-|-OOOO";
        arr[1] = "O-|O-OOO";
        arr[2] = "O-|OO-OO";
        arr[3] = "O-|OOO-O";
        arr[4] = "O-|OOOO-";
        arr[5] = "-O|-OOOO";
        arr[6] = "-O|O-OOO";
        arr[7] = "-O|OO-OO";
        arr[8] = "-O|OOO-O";
        arr[9] = "-O|OOOO-";
        Scanner scn = new Scanner(System.in);
        int n = scn.nextInt();
        if (n == 0) {
            System.out.println(arr[0]);
        } else
            while (n > 0) {
                int rem = n % 10;
                n = n / 10;
                System.out.println(arr[rem]);
            }
    }
}